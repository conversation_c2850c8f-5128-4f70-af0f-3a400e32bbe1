"""
Modern memory service using LangGraph persistence with user-wise conversation tracking
Based on <PERSON><PERSON><PERSON><PERSON>'s new memory migration guide
"""
from datetime import datetime
from typing import Dict, List, Any, Optional, Sequence
from langchain_core.messages import BaseMessage, HumanMessage, AIMessage, SystemMessage
from langchain_core.chat_history import BaseChatMessageHistory
from langgraph.checkpoint.memory import MemorySaver
from langgraph.checkpoint.base import BaseCheckpointSaver
import json
import asyncio
from collections import defaultdict

from src.models.conversation import ConversationService, ConversationCreate, ConversationInDB
from src.models.message import MessageService, MessageCreate, MessageType
from src.config.database import get_collection, COLLECTIONS

class UserChatMessageHistory(BaseChatMessageHistory):
    """
    User-specific chat message history implementation for LangGraph
    Stores messages per user and conversation
    """
    
    def __init__(self, user_id: str, conversation_id: str, message_service: MessageService):
        self.user_id = user_id
        self.conversation_id = conversation_id
        self.message_service = message_service
        self._messages: List[BaseMessage] = []
        self._loaded = False
    
    async def _load_messages(self):
        """Load messages from database if not already loaded"""
        if not self._loaded:
            # Get messages for this conversation
            db_messages = await self.message_service.get_conversation_messages(
                self.conversation_id, limit=100
            )
            
            # Convert to LangChain messages
            self._messages = []
            for msg in db_messages:
                if msg.message_type == MessageType.USER:
                    self._messages.append(HumanMessage(content=msg.content))
                elif msg.message_type == MessageType.ASSISTANT:
                    self._messages.append(AIMessage(content=msg.content))
                elif msg.message_type == MessageType.SYSTEM:
                    self._messages.append(SystemMessage(content=msg.content))
            
            self._loaded = True
    
    @property
    def messages(self) -> List[BaseMessage]:
        """Get messages (synchronous property)"""
        if not self._loaded:
            # For synchronous access, return cached messages
            return self._messages
        return self._messages
    
    async def aget_messages(self) -> List[BaseMessage]:
        """Get messages asynchronously"""
        await self._load_messages()
        return self._messages
    
    def add_message(self, message: BaseMessage) -> None:
        """Add a message to the history"""
        self._messages.append(message)
        # Note: Actual persistence happens in the chat service
    
    async def aadd_message(self, message: BaseMessage) -> None:
        """Add a message to the history asynchronously"""
        self.add_message(message)
    
    def add_messages(self, messages: Sequence[BaseMessage]) -> None:
        """Add multiple messages to the history"""
        self._messages.extend(messages)
    
    async def aadd_messages(self, messages: Sequence[BaseMessage]) -> None:
        """Add multiple messages to the history asynchronously"""
        self.add_messages(messages)
    
    def clear(self) -> None:
        """Clear the message history"""
        self._messages.clear()
    
    async def aclear(self) -> None:
        """Clear the message history asynchronously"""
        self.clear()

class UserMemoryManager:
    """
    User-wise memory manager using LangGraph persistence
    Manages conversations and memory per user
    """
    
    def __init__(self):
        self.conversation_service: Optional[ConversationService] = None
        self.message_service: Optional[MessageService] = None
        self.checkpointer = MemorySaver()
        self._user_histories: Dict[str, Dict[str, UserChatMessageHistory]] = defaultdict(dict)
    
    async def initialize(self):
        """Initialize the memory manager with database services"""
        conversations_collection = await get_collection("conversations")
        messages_collection = await get_collection(COLLECTIONS['messages'])
        
        self.conversation_service = ConversationService(
            conversations_collection, 
            messages_collection
        )
        self.message_service = MessageService(messages_collection)
    
    async def get_user_conversation_history(
        self, 
        user_id: str, 
        conversation_id: str
    ) -> UserChatMessageHistory:
        """Get or create conversation history for a user"""
        if not self.message_service:
            await self.initialize()
        
        # Check if we already have this history cached
        if conversation_id in self._user_histories[user_id]:
            return self._user_histories[user_id][conversation_id]
        
        # Create new history instance
        history = UserChatMessageHistory(
            user_id=user_id,
            conversation_id=conversation_id,
            message_service=self.message_service
        )
        
        # Cache it
        self._user_histories[user_id][conversation_id] = history
        
        return history
    
    async def create_conversation(
        self, 
        user_id: str, 
        session_id: str, 
        initial_message: Optional[str] = None
    ) -> ConversationInDB:
        """Create a new conversation for a user"""
        if not self.conversation_service:
            await self.initialize()
        
        conversation_data = ConversationCreate(
            session_id=session_id,
            user_id=user_id,
            initial_message=initial_message
        )
        
        return await self.conversation_service.create_conversation(conversation_data)
    
    async def get_user_conversations(
        self, 
        user_id: str, 
        limit: int = 50
    ) -> List[ConversationInDB]:
        """Get all conversations for a user"""
        if not self.conversation_service:
            await self.initialize()
        
        return await self.conversation_service.get_user_conversations(user_id, limit)
    
    async def get_conversation_by_session(self, session_id: str) -> Optional[ConversationInDB]:
        """Get conversation by session ID"""
        if not self.conversation_service:
            await self.initialize()
        
        return await self.conversation_service.get_conversation_by_session(session_id)
    
    async def update_conversation_metrics(self, session_id: str):
        """Update conversation metrics"""
        if not self.conversation_service:
            await self.initialize()
        
        await self.conversation_service.update_conversation_metrics(session_id)
    
    def get_checkpointer(self) -> BaseCheckpointSaver:
        """Get the LangGraph checkpointer for persistence"""
        return self.checkpointer
    
    async def trim_conversation_history(
        self, 
        user_id: str, 
        conversation_id: str, 
        max_messages: int = 20
    ) -> List[BaseMessage]:
        """
        Trim conversation history to keep only the most recent messages
        This implements the functionality of ConversationBufferWindowMemory
        """
        history = await self.get_user_conversation_history(user_id, conversation_id)
        messages = await history.aget_messages()
        
        if len(messages) <= max_messages:
            return messages
        
        # Keep the most recent messages
        return messages[-max_messages:]
    
    async def summarize_conversation_history(
        self, 
        user_id: str, 
        conversation_id: str,
        llm,
        max_token_limit: int = 2000
    ) -> str:
        """
        Summarize conversation history when it gets too long
        This implements the functionality of ConversationSummaryMemory
        """
        history = await self.get_user_conversation_history(user_id, conversation_id)
        messages = await history.aget_messages()
        
        if not messages:
            return ""
        
        # Convert messages to text for summarization
        conversation_text = "\n".join([
            f"{'Human' if isinstance(msg, HumanMessage) else 'AI'}: {msg.content}"
            for msg in messages
        ])
        
        # Use LLM to summarize
        summary_prompt = f"""
        Please provide a concise summary of the following conversation:
        
        {conversation_text}
        
        Summary:
        """
        
        try:
            summary = await llm.ainvoke(summary_prompt)
            return summary.content if hasattr(summary, 'content') else str(summary)
        except Exception as e:
            print(f"Error summarizing conversation: {e}")
            return "Conversation summary unavailable"
    
    async def get_conversation_context(
        self, 
        user_id: str, 
        conversation_id: str,
        context_type: str = "recent",
        max_messages: int = 10
    ) -> List[BaseMessage]:
        """
        Get conversation context based on different strategies
        
        Args:
            user_id: User ID
            conversation_id: Conversation ID
            context_type: "recent", "summary", or "full"
            max_messages: Maximum number of messages to return
        """
        if context_type == "recent":
            return await self.trim_conversation_history(
                user_id, conversation_id, max_messages
            )
        elif context_type == "full":
            history = await self.get_user_conversation_history(user_id, conversation_id)
            return await history.aget_messages()
        else:
            # Default to recent
            return await self.trim_conversation_history(
                user_id, conversation_id, max_messages
            )

# Global memory manager instance
memory_manager = None

async def get_memory_manager() -> UserMemoryManager:
    """Get or create the global memory manager instance"""
    global memory_manager
    if memory_manager is None:
        memory_manager = UserMemoryManager()
        await memory_manager.initialize()
    return memory_manager

def get_user_session_id(user_id: str, conversation_id: str) -> str:
    """Generate a unique session ID for LangGraph based on user and conversation"""
    return f"user_{user_id}_conv_{conversation_id}"
