"""
PostgreSQL-based checkpoint configuration for LangGraph
Production-ready persistent checkpointing
"""
import os
import asyncio
from typing import Optional
from langgraph.checkpoint.postgres.aio import AsyncPostgresSaver
from langgraph.checkpoint.postgres import PostgresSaver
from langgraph.checkpoint.memory import MemorySaver
import logging

logger = logging.getLogger(__name__)

class CheckpointConfig:
    """Configuration for LangGraph checkpointing"""
    
    def __init__(self):
        self.postgres_url = os.getenv("POSTGRES_CHECKPOINT_URL")
        self.use_postgres = bool(self.postgres_url)
        self._async_checkpointer: Optional[AsyncPostgresSaver] = None
        self._sync_checkpointer: Optional[PostgresSaver] = None
        self._memory_checkpointer: Optional[MemorySaver] = None
    
    async def get_async_checkpointer(self) -> AsyncPostgresSaver:
        """Get async PostgreSQL checkpointer"""
        if not self.use_postgres:
            raise ValueError("PostgreSQL URL not configured. Set POSTGRES_CHECKPOINT_URL environment variable.")
        
        if self._async_checkpointer is None:
            try:
                self._async_checkpointer = AsyncPostgresSaver.from_conn_string(
                    self.postgres_url,
                    # Optional: customize table name
                    # table_name="langgraph_checkpoints"
                )
                # Setup the database tables
                await self._async_checkpointer.setup()
                logger.info("✅ PostgreSQL async checkpointer initialized")
            except Exception as e:
                logger.error(f"❌ Failed to initialize PostgreSQL async checkpointer: {e}")
                raise
        
        return self._async_checkpointer
    
    def get_sync_checkpointer(self) -> PostgresSaver:
        """Get sync PostgreSQL checkpointer"""
        if not self.use_postgres:
            raise ValueError("PostgreSQL URL not configured. Set POSTGRES_CHECKPOINT_URL environment variable.")
        
        if self._sync_checkpointer is None:
            try:
                self._sync_checkpointer = PostgresSaver.from_conn_string(
                    self.postgres_url,
                    # Optional: customize table name
                    # table_name="langgraph_checkpoints"
                )
                # Setup the database tables
                self._sync_checkpointer.setup()
                logger.info("✅ PostgreSQL sync checkpointer initialized")
            except Exception as e:
                logger.error(f"❌ Failed to initialize PostgreSQL sync checkpointer: {e}")
                raise
        
        return self._sync_checkpointer
    
    def get_memory_checkpointer(self) -> MemorySaver:
        """Get in-memory checkpointer (fallback for development)"""
        if self._memory_checkpointer is None:
            self._memory_checkpointer = MemorySaver()
            logger.warning("⚠️  Using in-memory checkpointer. For production, configure POSTGRES_CHECKPOINT_URL.")
        
        return self._memory_checkpointer
    
    async def get_checkpointer(self):
        """Get the appropriate checkpointer based on configuration"""
        if self.use_postgres:
            return await self.get_async_checkpointer()
        else:
            return self.get_memory_checkpointer()
    
    async def close(self):
        """Close checkpointer connections"""
        if self._async_checkpointer:
            await self._async_checkpointer.aclose()
        if self._sync_checkpointer:
            self._sync_checkpointer.close()

# Global checkpoint config instance
checkpoint_config = CheckpointConfig()

async def get_checkpointer():
    """Get the configured checkpointer"""
    return await checkpoint_config.get_checkpointer()

async def close_checkpointer():
    """Close checkpointer connections"""
    await checkpoint_config.close()

# Environment variable examples for .env file:
"""
# For PostgreSQL checkpointing (recommended for production)
POSTGRES_CHECKPOINT_URL=postgresql://username:password@localhost:5432/langgraph_checkpoints

# Alternative formats:
# POSTGRES_CHECKPOINT_URL=postgresql://user:pass@localhost/dbname
# POSTGRES_CHECKPOINT_URL=postgresql://user:pass@localhost:5432/dbname?sslmode=require

# For development without PostgreSQL, leave this unset to use in-memory checkpointing
"""
